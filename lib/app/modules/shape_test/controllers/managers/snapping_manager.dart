import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../models/shape_data.dart';
import '../../utils/geometry_utils.dart';
import '../../painters/grid_system.dart';
import '../../handlers/shape_manipulation_handlers.dart'; // For SnapInfo, SnapType

// TODO: Define a result class/record for snap calculations
// typedef SnapResult = ({ Offset snappedPosition, SnapInfo? snapInfo, bool didCenterSnap });
// typedef DragSnapResult = ({ ShapeData snappedShapeData, SnapInfo? snapInfo, bool didCenterSnap });

/// Manages all snapping logic for shapes and points within the shape editor.
/// This includes snapping to the grid, center lines, and other shapes.
class SnappingManager {
  final GridSystem gridSystem;
  // TODO: Add other necessary dependencies like thresholds, enabled flags

  // --- Snap State (for drag operations) ---
  // Stores SnapInfo, 'centerSnap', or null for each active drag Key
  final Map<Key, dynamic> _currentSnapStates = {};
  // Stores the pointer's global position when a snap was initiated for a Key
  final Map<Key, Offset> _pointerPositionAtSnap = {};
  // Threshold in logical pixels to move pointer before releasing an active snap
  final double _snapReleaseThreshold =
      6.0; // Reduced from 8.0 for even easier unsnapping - especially for shape-to-shape

  // Define separate thresholds for different snap types
  final double _shapeToShapeReleaseThreshold =
      4.0; // Lower threshold for shape-to-shape snaps (was 5.0)
  final double _centerReleaseThreshold = 6.0; // Keep original for center snaps

  SnappingManager({required this.gridSystem});

  /// Snaps a single point to the grid or center lines based on configuration.
  /// Replaces the snapping logic previously in GridSystem.
  ///
  /// Args:
  ///   globalPoint: The point in global screen coordinates.
  ///   canvasSize: The size of the canvas.
  ///
  /// Returns:
  ///   The snapped point in global screen coordinates.
  Offset snapPoint(Offset globalPoint, Size canvasSize) {
    // TODO: Implement point snapping (Grid + Center) using GridSystem properties
    // Move logic from GridSystem.snapPointToGrid, _snapToCenterLine, _snapToNeedleGrid here.

    // Placeholder implementation:
    // return globalPoint;

    // --- Start Moved Logic from GridSystem ---
    if (!gridSystem.snapToGrid && !gridSystem.snapToCenter) return globalPoint;

    // Convert to grid space
    final gridPoint = gridSystem.screenToGridPoint(globalPoint, canvasSize);

    // Check for center line snapping first
    if (gridSystem.snapToCenter) {
      final centerSnappedGridPoint = _snapToCenterLine(gridPoint, canvasSize);
      if (centerSnappedGridPoint != null) {
        return gridSystem.gridToScreenPoint(centerSnappedGridPoint, canvasSize);
      }
    }

    // If not snapping to grid, return original point
    if (!gridSystem.snapToGrid) return globalPoint;

    final snappedGridPoint = _snapToNeedleGrid(gridPoint, canvasSize);
    return gridSystem.gridToScreenPoint(snappedGridPoint, canvasSize);
    // --- End Moved Logic from GridSystem ---
  }

  /// Calculates the snap adjustment for a shape being dragged.
  /// Determines if the shape should snap to the grid, center, or other shapes.
  /// This method calculates the *result* of the snap but doesn't modify the input ShapeData.
  /// It also handles the snap state persistence (holding the snap).
  ///
  /// Args:
  ///   originalShapeData: The shape data *before* the current drag delta is applied.
  ///   potentialShapeData: The shape data *after* the drag delta and constraints are applied,
  ///                       but *before* snapping.
  ///   globalPointerPosition: The current global position of the user's pointer/finger.
  ///   shapeKey: The key of the shape being dragged.
  ///   allShapeStates: Map of all shapes for shape-to-shape snapping checks.
  ///   constraints: The bounding constraints for the drag operation.
  ///
  /// Returns:
  ///   A record containing the necessary translation Offset to apply for snapping,
  ///   optional SnapInfo for visual feedback, and a flag indicating center snap.
  ({Offset snapOffset, SnapInfo? snapInfo, bool didCenterSnap})
      calculateDragSnap({
    required ShapeData originalShapeData,
    required ShapeData potentialShapeData,
    required Offset globalPointerPosition,
    required Key shapeKey,
    required Map<Key, ShapeData> allShapeStates,
    required BoxConstraints constraints,
    // --- Snap Configuration ---
    // TODO: Consider moving these to a dedicated config object or SnappingManager properties
    double shapeSnapThreshold = 10.0, // Reduced from 12.0 for easier unsnapping
    double centerSnapThreshold =
        12.0, // Reduced from 15.0 for easier unsnapping
    double gridSnapThreshold = 8.0, // Reduced from 10.0 for easier unsnapping
    bool isCenterSnapEnabled = true,
    bool isShapeSnapEnabled = true,
    bool isGridSnapEnabled = true, // Flag to enable/disable grid snapping
  }) {
    Offset calculatedSnapOffset = Offset.zero;
    SnapInfo? winningSnapInfo;
    bool didPerformCenterSnap = false; // Specifically tracks canvas center snap

    // --- 1. Check existing snap state and release threshold ---
    final activeSnap = _currentSnapStates[shapeKey];
    bool applyForcedSnap = false;

    if (activeSnap != null) {
      final initialSnapPointerPos = _pointerPositionAtSnap[shapeKey];
      if (initialSnapPointerPos != null) {
        final distanceMoved =
            (globalPointerPosition - initialSnapPointerPos).distance;

        // Determine appropriate release threshold based on snap type
        double releaseThreshold = _snapReleaseThreshold; // Default

        if (activeSnap is SnapInfo) {
          // Use shape-to-shape threshold for shape-to-shape snaps
          if (activeSnap.targetShapeKey != null) {
            releaseThreshold = _shapeToShapeReleaseThreshold;
          }
          // Use center threshold for center snaps
          else if (activeSnap.snapType == SnapType.canvasCenterHorizontal ||
              activeSnap.snapType == SnapType.canvasCenterVertical) {
            releaseThreshold = _centerReleaseThreshold;
          }
        }

        if (distanceMoved < releaseThreshold) {
          applyForcedSnap = true;
          // If forcing snap, revert to the original position (where it was snapped)
          // The required offset is the difference between the potential *new* center
          // and the original center where the snap occurred.
          calculatedSnapOffset =
              originalShapeData.center - potentialShapeData.center;

          // Restore snap info for visual feedback during forced snap
          if (activeSnap is SnapInfo) {
            winningSnapInfo = activeSnap;
            // Check if the forced snap was a canvas center snap
            didPerformCenterSnap =
                activeSnap.snapType == SnapType.canvasCenterHorizontal ||
                    activeSnap.snapType == SnapType.canvasCenterVertical;
          }
          // Handle old 'centerSnap' string state if necessary (can be removed later)
          else if (activeSnap == 'centerSnap') {
            winningSnapInfo = SnapInfo(
              targetShapeKey: shapeKey, // Or null?
              snapType: SnapType.canvasCenterHorizontal, // Assume horizontal
              snapOffset: calculatedSnapOffset.dx,
              isHorizontal: true,
            );
            didPerformCenterSnap = true;
          }
        } else {
          // Exceeded release threshold, clear state
          resetDragSnapState(shapeKey);
        }
      } else {
        // Inconsistent state, clear it
        resetDragSnapState(shapeKey);
      }
    }

    // --- 2. If not forcing a previous snap, calculate potential new snaps ---
    if (!applyForcedSnap) {
      final currentBounds = potentialShapeData.boundingRect;
      final currentCenter = potentialShapeData.center;

      double bestSnapDx = 0;
      double bestSnapDy = 0;
      double minSnapDistX = double.infinity; // Start with infinity
      double minSnapDistY = double.infinity; // Start with infinity
      SnapInfo? horizontalSnap;
      SnapInfo? verticalSnap;
      bool centerSnapXActivated = false;
      bool centerSnapYActivated =
          false; // Tracks if vertical center snap is the current best V snap

      final canvasSize = Size(constraints.maxWidth, constraints.maxHeight);
      final canvasCenterX = canvasSize.width / 2;
      final canvasCenterY = canvasSize.height / 2;

      // Standardized parameters for snap behavior - reduced for less aggressive snapping
      final double centerMagneticFactor = 2.2; // For center snaps
      final double shapeMagneticFactor =
          1.8; // Reduced magnetic factor for shape-to-shape snaps
      final double directSnapThreshold = shapeSnapThreshold *
          0.4; // Reduced further from 0.5 for smaller direct snap zone

      // --- Calculate Potential Snaps ---

      // a) Canvas Center Snapping (Both Horizontal and Vertical)
      if (isCenterSnapEnabled && centerSnapThreshold > 0) {
        // Horizontal Canvas Center Snap
        final dxCenterToCanvasCenter = currentCenter.dx - canvasCenterX;
        if (dxCenterToCanvasCenter.abs() < centerSnapThreshold) {
          // Enhanced direct snap with stronger magnetic effect near threshold
          final effectiveDistanceRatio =
              dxCenterToCanvasCenter.abs() / centerSnapThreshold;

          // Apply stronger pull when close to threshold
          double snapStrength;
          if (effectiveDistanceRatio < 0.3) {
            // Very close - complete snap
            snapStrength = 1.0;
          } else {
            // Apply progressive pull with exponential curve for more natural feel
            snapStrength = math
                .pow(1.0 - effectiveDistanceRatio, centerMagneticFactor)
                .toDouble();
          }

          final directSnapDx = -dxCenterToCanvasCenter * snapStrength;

          // --- DEBUG LOG for Horizontal Center Snap Activation ---
          debugPrint(
              "-> H-Snap ACTIVATED! diff=${dxCenterToCanvasCenter.toStringAsFixed(2)}, directSnapDx=${directSnapDx.toStringAsFixed(2)}, strength=${snapStrength.toStringAsFixed(2)}");
          // --- END DEBUG LOG ---

          minSnapDistX = dxCenterToCanvasCenter.abs();
          bestSnapDx = directSnapDx; // Use direct snap offset
          horizontalSnap = SnapInfo(
              targetShapeKey: null, // No target shape for canvas center
              snapType: SnapType.canvasCenterHorizontal,
              snapOffset: bestSnapDx, // Store the calculated magnetic offset
              isHorizontal: true);
          centerSnapXActivated = true;
        }

        // Vertical Canvas Center Snap - enhanced with the same logic
        final dyCenterToCanvasCenter = currentCenter.dy - canvasCenterY;
        if (dyCenterToCanvasCenter.abs() < centerSnapThreshold) {
          // Enhanced direct snap with stronger magnetic effect
          final effectiveDistanceRatio =
              dyCenterToCanvasCenter.abs() / centerSnapThreshold;

          // Apply stronger pull when close to threshold
          double snapStrength;
          if (effectiveDistanceRatio < 0.3) {
            // Very close - complete snap
            snapStrength = 1.0;
          } else {
            // Apply progressive pull with exponential curve
            snapStrength = math
                .pow(1.0 - effectiveDistanceRatio, centerMagneticFactor)
                .toDouble();
          }

          final directSnapDy = -dyCenterToCanvasCenter * snapStrength;

          debugPrint(
              "-> V-Snap ACTIVATED! diff=${dyCenterToCanvasCenter.toStringAsFixed(2)}, directSnapDy=${directSnapDy.toStringAsFixed(2)}, strength=${snapStrength.toStringAsFixed(2)}");

          minSnapDistY = dyCenterToCanvasCenter.abs();
          bestSnapDy = directSnapDy; // Use direct snap offset
          verticalSnap = SnapInfo(
              targetShapeKey: null,
              snapType: SnapType.canvasCenterVertical,
              snapOffset: bestSnapDy, // Store calculated magnetic offset
              isHorizontal: false);
          centerSnapYActivated = true;
        }
      }

      // b) Shape-to-Shape Snapping
      if (isShapeSnapEnabled) {
        allShapeStates.forEach((key, otherShapeData) {
          if (key == shapeKey) return; // Don't snap to self

          final otherBoundsResult =
              GeometryUtils.calculateAccurateBoundingRect(otherShapeData);
          Rect otherBounds;
          if (otherBoundsResult is GroupBoundsData) {
            otherBounds = otherBoundsResult.bounds;
          } else {
            // Assume it's a Rect, though add error handling if needed
            otherBounds = otherBoundsResult as Rect;
          }

          // --- Horizontal Shape Snaps ---
          final dxLeftToRight = currentBounds.left - otherBounds.right;
          final dxRightToLeft = currentBounds.right - otherBounds.left;
          final dxLeftToLeft = currentBounds.left - otherBounds.left;
          final dxRightToRight = currentBounds.right - otherBounds.right;
          final dxCenterToCenter = currentCenter.dx - otherBounds.center.dx;

          // Apply enhanced snap logic to all edge alignments with shape-specific magnetic factor
          _checkAndApplyHorizontalSnap(
              dxLeftToRight,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistX,
              centerSnapXActivated,
              SnapType.leftToRight,
              key, (snapDx, snapDist) {
            if (snapDist < minSnapDistX) {
              minSnapDistX = snapDist;
              bestSnapDx = snapDx;
              horizontalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.leftToRight,
                  snapOffset: bestSnapDx,
                  isHorizontal: true);
              centerSnapXActivated = false;
              debugPrint(
                  "Left-to-Right Shape Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDx.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          _checkAndApplyHorizontalSnap(
              dxRightToLeft,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistX,
              centerSnapXActivated,
              SnapType.rightToLeft,
              key, (snapDx, snapDist) {
            if (snapDist < minSnapDistX) {
              minSnapDistX = snapDist;
              bestSnapDx = snapDx;
              horizontalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.rightToLeft,
                  snapOffset: bestSnapDx,
                  isHorizontal: true);
              centerSnapXActivated = false;
              debugPrint(
                  "Right-to-Left Shape Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDx.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          _checkAndApplyHorizontalSnap(
              dxLeftToLeft,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistX,
              centerSnapXActivated,
              SnapType.leftToLeft,
              key, (snapDx, snapDist) {
            if (snapDist < minSnapDistX) {
              minSnapDistX = snapDist;
              bestSnapDx = snapDx;
              horizontalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.leftToLeft,
                  snapOffset: bestSnapDx,
                  isHorizontal: true);
              centerSnapXActivated = false;
              debugPrint(
                  "Left-to-Left Shape Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDx.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          _checkAndApplyHorizontalSnap(
              dxRightToRight,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistX,
              centerSnapXActivated,
              SnapType.rightToRight,
              key, (snapDx, snapDist) {
            if (snapDist < minSnapDistX) {
              minSnapDistX = snapDist;
              bestSnapDx = snapDx;
              horizontalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.rightToRight,
                  snapOffset: bestSnapDx,
                  isHorizontal: true);
              centerSnapXActivated = false;
              debugPrint(
                  "Right-to-Right Shape Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDx.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          _checkAndApplyHorizontalSnap(
              dxCenterToCenter,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistX,
              centerSnapXActivated,
              SnapType.horizontalCenter,
              key, (snapDx, snapDist) {
            if (snapDist < minSnapDistX) {
              minSnapDistX = snapDist;
              bestSnapDx = snapDx;
              horizontalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.horizontalCenter,
                  snapOffset: bestSnapDx,
                  isHorizontal: true);
              centerSnapXActivated = false;
              debugPrint(
                  "Horizontal Center Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDx.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          // --- Vertical Shape Snaps ---
          final dyTopToBottom = currentBounds.top - otherBounds.bottom;
          final dyBottomToTop = currentBounds.bottom - otherBounds.top;
          final dyTopToTop = currentBounds.top - otherBounds.top;
          final dyBottomToBottom = currentBounds.bottom - otherBounds.bottom;
          final dyCenterToCenter = currentCenter.dy - otherBounds.center.dy;

          // Apply enhanced snap logic to all vertical edge alignments
          _checkAndApplyVerticalSnap(
              dyTopToBottom,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistY,
              centerSnapYActivated,
              SnapType.topToBottom,
              key, (snapDy, snapDist) {
            if (snapDist < minSnapDistY) {
              minSnapDistY = snapDist;
              bestSnapDy = snapDy;
              verticalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.topToBottom,
                  snapOffset: bestSnapDy,
                  isHorizontal: false);
              centerSnapYActivated = false;
              debugPrint(
                  "Top-to-Bottom Shape Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDy.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          _checkAndApplyVerticalSnap(
              dyBottomToTop,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistY,
              centerSnapYActivated,
              SnapType.bottomToTop,
              key, (snapDy, snapDist) {
            if (snapDist < minSnapDistY) {
              minSnapDistY = snapDist;
              bestSnapDy = snapDy;
              verticalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.bottomToTop,
                  snapOffset: bestSnapDy,
                  isHorizontal: false);
              centerSnapYActivated = false;
              debugPrint(
                  "Bottom-to-Top Shape Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDy.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          _checkAndApplyVerticalSnap(
              dyTopToTop,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistY,
              centerSnapYActivated,
              SnapType.topToTop,
              key, (snapDy, snapDist) {
            if (snapDist < minSnapDistY) {
              minSnapDistY = snapDist;
              bestSnapDy = snapDy;
              verticalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.topToTop,
                  snapOffset: bestSnapDy,
                  isHorizontal: false);
              centerSnapYActivated = false;
              debugPrint(
                  "Top-to-Top Shape Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDy.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          _checkAndApplyVerticalSnap(
              dyBottomToBottom,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistY,
              centerSnapYActivated,
              SnapType.bottomToBottom,
              key, (snapDy, snapDist) {
            if (snapDist < minSnapDistY) {
              minSnapDistY = snapDist;
              bestSnapDy = snapDy;
              verticalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.bottomToBottom,
                  snapOffset: bestSnapDy,
                  isHorizontal: false);
              centerSnapYActivated = false;
              debugPrint(
                  "Bottom-to-Bottom Shape Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDy.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });

          _checkAndApplyVerticalSnap(
              dyCenterToCenter,
              shapeSnapThreshold,
              directSnapThreshold,
              shapeMagneticFactor, // Use shape-specific magnetic factor
              minSnapDistY,
              centerSnapYActivated,
              SnapType.verticalCenter,
              key, (snapDy, snapDist) {
            if (snapDist < minSnapDistY) {
              minSnapDistY = snapDist;
              bestSnapDy = snapDy;
              verticalSnap = SnapInfo(
                  targetShapeKey: key,
                  snapType: SnapType.verticalCenter,
                  snapOffset: bestSnapDy,
                  isHorizontal: false);
              centerSnapYActivated = false;
              debugPrint(
                  "Vertical Center Snap: distance=${snapDist.toStringAsFixed(2)}, offset=${bestSnapDy.toStringAsFixed(2)}");
              return true;
            }
            return false;
          });
        });
      }

      // --- 3. Determine Winning Snap Offset and Info ---
      calculatedSnapOffset = Offset.zero; // Reset before applying winners

      // If a horizontal snap was found (could be center, shape, or grid)
      if (horizontalSnap != null && minSnapDistX != double.infinity) {
        calculatedSnapOffset =
            Offset(horizontalSnap!.snapOffset, 0); // Use ! assertion
        winningSnapInfo = horizontalSnap;
        didPerformCenterSnap = horizontalSnap!.snapType ==
            SnapType.canvasCenterHorizontal; // Use ! assertion
      }

      // If a vertical snap was found (could be shape or grid)
      if (verticalSnap != null && minSnapDistY != double.infinity) {
        // --- Refined Logic: Combine or Prioritize? ---
        // Option A: Combine H and V snaps if they exist independently
        calculatedSnapOffset = Offset(calculatedSnapOffset.dx,
            verticalSnap!.snapOffset); // Use ! assertion

        // For now, prioritize the 'closer' snap for the single winningSnapInfo,
        // but apply both offsets.
        // Prioritize based on distance *before* magnetic pull was applied for fairness
        final comparisonDistX =
            horizontalSnap?.snapType == SnapType.canvasCenterHorizontal
                ? minSnapDistX
                : (horizontalSnap?.snapOffset.abs() ?? double.infinity);
        final comparisonDistY =
            verticalSnap?.snapType == SnapType.canvasCenterVertical
                ? minSnapDistY
                : (verticalSnap?.snapOffset.abs() ?? double.infinity);

        if (winningSnapInfo == null || comparisonDistY < comparisonDistX) {
          winningSnapInfo = verticalSnap; // Safe: verticalSnap is not null
          // Keep didPerformCenterSnap status from horizontal check if H snap also occurred
        }
        // If horizontal snap exists and is closer, winningSnapInfo is already set.
        // didPerformCenterSnap remains as set by the horizontal check.
      }

      // --- 4. If a new snap occurred, store its state ---
      if (calculatedSnapOffset != Offset.zero) {
        // Store the snap state. Use the winningSnapInfo if available.
        if (winningSnapInfo != null) {
          _currentSnapStates[shapeKey] =
              winningSnapInfo; // Store the SnapInfo object
          _pointerPositionAtSnap[shapeKey] = globalPointerPosition;
        } else {
          // Should not happen if offset is non-zero, but clear state just in case
          resetDragSnapState(shapeKey);
        }
      } else {
        // No snap occurred, ensure state is clear
        resetDragSnapState(shapeKey);
      }
    } // End !applyForcedSnap

    // --- 5. Return results ---
    // Ensure didPerformCenterSnap accurately reflects if the *final* horizontal snap was canvas center.
    // It might have been overridden by a closer shape/grid snap.
    // Also check vertical center snap.
    bool finalDidCenterSnap = false;
    if (calculatedSnapOffset.dx != 0 &&
        winningSnapInfo?.isHorizontal == true &&
        winningSnapInfo?.snapType == SnapType.canvasCenterHorizontal) {
      finalDidCenterSnap = true;
    }
    if (calculatedSnapOffset.dy != 0 &&
        winningSnapInfo?.isHorizontal == false &&
        winningSnapInfo?.snapType == SnapType.canvasCenterVertical) {
      // If there's also a horizontal snap, we might want to indicate both somehow?
      // For now, let's say center snap is true if EITHER axis snapped to center.
      finalDidCenterSnap = true;
    }
    didPerformCenterSnap =
        finalDidCenterSnap; // Assign the final calculated value

    // Debug log the final snap result
    if (calculatedSnapOffset != Offset.zero) {
      debugPrint(
          "FINAL SNAP: Offset=$calculatedSnapOffset, Type=${winningSnapInfo?.snapType}, CenterSnap=$didPerformCenterSnap");
    }

    return (
      snapOffset: calculatedSnapOffset,
      snapInfo: winningSnapInfo,
      didCenterSnap: didPerformCenterSnap // Return the final center snap status
    );
  }

  /// Helper method to check and apply a horizontal snap
  bool _checkAndApplyHorizontalSnap(
      double distanceValue,
      double threshold,
      double directThreshold,
      double magneticFactor,
      double currentMinDist,
      bool centerSnapActive,
      SnapType snapType,
      Key targetKey,
      bool Function(double snapValue, double snapDistance) updateCallback) {
    if ((!centerSnapActive || distanceValue.abs() < currentMinDist) &&
        distanceValue.abs() < threshold) {
      // Calculate snap value based on distance - Reduced for easier unsnapping
      double effectiveSnapValue;

      // Calculate distance ratio within the threshold (0.0 to 1.0)
      final distanceRatio = distanceValue.abs() / threshold;

      if (distanceValue.abs() < directThreshold) {
        // Direct snap when very close - scale based on how close to direct threshold
        final directSnapRatio = distanceValue.abs() / directThreshold;
        // Apply a more gradual direct snap with less magnetic pull
        effectiveSnapValue = -distanceValue * (0.75 - (directSnapRatio * 0.25));
      } else {
        // Reduced magnetic pull with gentler attraction curve
        // Use a milder curve for more gentle pull
        // Apply a weaker pull for shape-to-shape snapping
        final pullStrength = math
            .pow(
                1 - distanceRatio,
                magneticFactor *
                    0.70 // Reduce effective magnetic factor further
                )
            .toDouble();

        // Apply progressive snap with more moderate effect
        effectiveSnapValue =
            -distanceValue * pullStrength * 0.8; // Reduce overall pull strength
      }

      // Let the callback handle the update logic
      return updateCallback(effectiveSnapValue, distanceValue.abs());
    }
    return false;
  }

  /// Helper method to check and apply a vertical snap
  bool _checkAndApplyVerticalSnap(
      double distanceValue,
      double threshold,
      double directThreshold,
      double magneticFactor,
      double currentMinDist,
      bool centerSnapActive,
      SnapType snapType,
      Key targetKey,
      bool Function(double snapValue, double snapDistance) updateCallback) {
    if ((!centerSnapActive || distanceValue.abs() < currentMinDist) &&
        distanceValue.abs() < threshold) {
      // Calculate snap value based on distance - Reduced for easier unsnapping
      double effectiveSnapValue;

      // Calculate distance ratio within the threshold (0.0 to 1.0)
      final distanceRatio = distanceValue.abs() / threshold;

      if (distanceValue.abs() < directThreshold) {
        // Direct snap when very close - scale based on how close to direct threshold
        final directSnapRatio = distanceValue.abs() / directThreshold;
        // Apply a more gradual direct snap with less magnetic pull
        effectiveSnapValue = -distanceValue * (0.75 - (directSnapRatio * 0.25));
      } else {
        // Reduced magnetic pull with gentler attraction curve
        // Use a milder curve for more gentle pull
        // Apply a weaker pull for shape-to-shape snapping
        final pullStrength = math
            .pow(
                1 - distanceRatio,
                magneticFactor *
                    0.70 // Reduce effective magnetic factor further
                )
            .toDouble();

        // Apply progressive snap with more moderate effect
        effectiveSnapValue =
            -distanceValue * pullStrength * 0.8; // Reduce overall pull strength
      }

      // Let the callback handle the update logic
      return updateCallback(effectiveSnapValue, distanceValue.abs());
    }
    return false;
  }

  /// Clears the internal snap state for a specific key, typically called on drag end.
  void resetDragSnapState(Key shapeKey) {
    _currentSnapStates.remove(shapeKey);
    _pointerPositionAtSnap.remove(shapeKey);
  }

  /// Clears all internal snap state.
  void clearAllSnapStates() {
    _currentSnapStates.clear();
    _pointerPositionAtSnap.clear();
  }

  // --- Private Helper Methods (Moved from GridSystem) ---

  /// Try to snap a point to the center line of the canvas (in grid coordinates).
  /// Returns the snapped grid coordinate if successful, otherwise null.
  Offset? _snapToCenterLine(Offset gridPoint, Size canvasSize) {
    // Calculate the absolute center of the canvas (independent of pan)
    final centerX = canvasSize.width / 2;
    final centerY = canvasSize.height / 2;

    // Calculate the screen center in grid coordinates
    final screenCenterInGrid =
        gridSystem.screenToGridPoint(Offset(centerX, centerY), canvasSize);

    // Calculate distances to center lines (in grid space)
    final distanceToVerticalCenter =
        (gridPoint.dx - screenCenterInGrid.dx).abs();
    final distanceToHorizontalCenter =
        (gridPoint.dy - screenCenterInGrid.dy).abs();

    // Adjust threshold based on zoom
    // Access zoomLevel via gridSystem
    final adjustedThreshold =
        gridSystem.centerSnapThreshold / math.sqrt(gridSystem.zoomLevel);

    // Reduced magnetic factor for gentler pull
    final magneticFactor = 2.0; // Reduced from 2.5 for gentler magnetic effect

    // Check vertical center line first
    if (distanceToVerticalCenter <= adjustedThreshold) {
      // Use a gentler exponential curve for milder magnetic effect
      final pullStrength = math.pow(
          1 - (distanceToVerticalCenter / adjustedThreshold), magneticFactor);
      final verticalCenterX = screenCenterInGrid.dx;

      // If very close to center line, snap with a slightly gradual approach
      if (distanceToVerticalCenter <= adjustedThreshold * 0.25) {
        // Apply 95% snap when very close instead of 100%
        final blendFactor = 0.95;
        return Offset(
            gridPoint.dx * (1 - blendFactor) + verticalCenterX * blendFactor,
            gridPoint.dy);
      }

      // Calculate the snapped position with reduced magnetic pull
      final snappedGridX =
          gridPoint.dx * (1 - pullStrength) + verticalCenterX * pullStrength;
      return Offset(snappedGridX, gridPoint.dy);
    }

    // Check horizontal center line
    if (distanceToHorizontalCenter <= adjustedThreshold) {
      // Use a gentler exponential curve for milder magnetic effect
      final pullStrength = math.pow(
          1 - (distanceToHorizontalCenter / adjustedThreshold), magneticFactor);
      final horizontalCenterY = screenCenterInGrid.dy;

      // If very close to center line, snap with a slightly gradual approach
      if (distanceToHorizontalCenter <= adjustedThreshold * 0.25) {
        // Apply 95% snap when very close instead of 100%
        final blendFactor = 0.95;
        return Offset(gridPoint.dx,
            gridPoint.dy * (1 - blendFactor) + horizontalCenterY * blendFactor);
      }

      // Calculate the snapped position with reduced magnetic pull
      final snappedGridY =
          gridPoint.dy * (1 - pullStrength) + horizontalCenterY * pullStrength;
      return Offset(gridPoint.dx, snappedGridY);
    }

    return null;
  }

  /// Snap to needle-based grid (in grid coordinates)
  Offset _snapToNeedleGrid(Offset gridPoint, Size canvasSize) {
    // Get properties from gridSystem
    final spacing = gridSystem.cellWidth;
    final needleCount = gridSystem.needleCount;
    final aspectRatio = gridSystem.aspectRatio;
    final snapThreshold = gridSystem.snapThreshold;
    final zoomLevel = gridSystem.zoomLevel;

    // Calculate the canvas center
    final centerX = canvasSize.width / 2;
    final centerY = canvasSize.height / 2;

    // Calculate total grid width based on needle count
    final totalWidth = needleCount * spacing;

    // Starting position to center the grid
    final leftEdge = gridSystem
        .screenToGridPoint(
            Offset(centerX - (totalWidth / 2), centerY), canvasSize)
        .dx; // Use gridSystem conversion

    // Find the nearest vertical grid line (needle)
    double nearestX = gridPoint.dx;
    double minDistanceX = double.infinity;

    for (int i = 0; i <= needleCount; i++) {
      final x = leftEdge + (i * spacing);
      final distance = (x - gridPoint.dx).abs();

      // Enhanced snap effect for grid - add progressive magnetic behavior
      final adjustedThreshold = snapThreshold / zoomLevel;
      if (distance <= adjustedThreshold) {
        // Apply stronger snap when closer to grid line
        final distanceRatio = distance / adjustedThreshold;
        final magneticFactor = 2.5; // Higher value means stronger pull

        if (distance < minDistanceX) {
          minDistanceX = distance;

          // Complete snap when very close
          if (distance < adjustedThreshold * 0.3) {
            nearestX = x;
          } else {
            // Progressive snap with magnetic effect
            final pullStrength =
                math.pow(1 - distanceRatio, magneticFactor).toDouble();
            nearestX = gridPoint.dx * (1 - pullStrength) + x * pullStrength;
          }
        }
      }
    }

    // Find the nearest horizontal grid line with enhanced snap effect
    final double validAspectRatio =
        (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
            ? 1.5
            : aspectRatio;
    final rowSpacing = spacing * validAspectRatio;
    final gridCenterY =
        gridSystem.screenToGridPoint(Offset(centerX, centerY), canvasSize).dy;
    final topEdgeOffset = gridCenterY % rowSpacing;
    double nearestY = gridPoint.dy;
    double minDistanceY = double.infinity;

    // Check a reasonable number of horizontal lines
    // Calculate grid height in canvas space
    final gridHeight = canvasSize.height / zoomLevel;
    final lineCount = (gridHeight / rowSpacing).ceil() + 2;
    final startLineIndex =
        ((gridPoint.dy - topEdgeOffset - gridHeight / 2) / rowSpacing).floor() -
            1;

    for (int i = startLineIndex; i <= startLineIndex + lineCount; i++) {
      final y = topEdgeOffset + (i * rowSpacing);
      final distance = (y - gridPoint.dy).abs();

      // Enhanced snap effect for horizontal grid lines
      final adjustedThreshold = snapThreshold / zoomLevel;
      if (distance <= adjustedThreshold) {
        // Apply stronger snap when closer to grid line
        final distanceRatio = distance / adjustedThreshold;
        final magneticFactor = 2.5; // Higher value means stronger pull

        if (distance < minDistanceY) {
          minDistanceY = distance;

          // Complete snap when very close
          if (distance < adjustedThreshold * 0.3) {
            nearestY = y;
          } else {
            // Progressive snap with magnetic effect
            final pullStrength =
                math.pow(1 - distanceRatio, magneticFactor).toDouble();
            nearestY = gridPoint.dy * (1 - pullStrength) + y * pullStrength;
          }
        }
      }
    }

    // Return the snapped point in grid space
    return Offset(nearestX, nearestY);
  }
}
