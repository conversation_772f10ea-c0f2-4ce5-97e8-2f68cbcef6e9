import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/managers/knitting_instructions_manager.dart';
import 'dart:math' as math;
import '../controllers/new_item_wizard_controller.dart';
import '../utils/knitting_utils.dart' as utils;
import '../utils/knitting_utils.dart' show StitchRange;
import '../../../utils/dimension_utils.dart';
import '../utils/zone_configuration_processor.dart';

import 'components/enhanced_pattern_visualizer.dart';
import 'widgets/zone_aware_progress_painter.dart';
import 'widgets/standard_dialog.dart';
import 'package:xoxknit/app/services/printer/pattern_printer_service.dart';

class InteractiveKnittingView extends GetView<NewItemWizardController> {
  InteractiveKnittingView({super.key});

  // Add a local controller for row completion tracking
  final RxList<bool> completedRows = <bool>[].obs;

  // Add controller for tab section expansion
  final RxBool isTabSectionExpanded = false.obs;

  // // Add tracking for the current zone
  // final RxInt controller.currentKnittingZoneIndex = 0.obs;

  // Add tracking for zone completion status
  final RxList<bool> completedZones = <bool>[].obs;

  // Method to initialize completed rows
  void _initializeCompletedRows(int count) {
    if (completedRows.length != count) {
      completedRows.value = List.generate(count, (_) => false);
    }
  }

  // Method to initialize zone completion tracking
  void _initializeZoneCompletion(int zoneCount) {
    if (completedZones.length != zoneCount) {
      // Initialize from controller's state if available, otherwise create new list
      if (controller.completedKnittingZones.length == zoneCount) {
        completedZones.value = List.from(controller.completedKnittingZones);
      } else {
        completedZones.value = List.generate(zoneCount, (_) => false);
        // Update controller's state to match
        controller.completedKnittingZones.value = List.from(completedZones);
      }
    }
  }

  // Method to mark current zone as completed
  void _markCurrentZoneCompleted() {
    if (controller.currentKnittingZoneIndex.value < completedZones.length) {
      // Update local state
      final newList = [...completedZones];
      newList[controller.currentKnittingZoneIndex.value] = true;
      completedZones.value = newList;

      // Synchronize with controller state
      controller.markZoneCompleted(controller.currentKnittingZoneIndex.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Try to ensure knitting instructions are available when this view is loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.knittingInstructions.value.isEmpty) {
        // Try to regenerate knitting instructions if they're missing
        controller.generateKnittingInstructions();
      }

      // Ensure zone index is valid
      _ensureValidZoneIndex();
    });

    return Scaffold(
      appBar: _buildAppBar(context),
      body: Obx(() {
        // Show loading indicator if controller is loading
        if (controller.isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: Get.theme.colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  'knittingInstructions_interactive_loadingInstructions'.tr,
                  style: TextStyle(
                    color: Get.theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          );
        }

        // Get the knitting zones
        final List<KnittingZone> zones = [];
        if (Get.isRegistered<ShapeEditorController>()) {
          final shapeController = Get.find<ShapeEditorController>();
          zones.addAll(
              shapeController.knittingInstructionsManager.knittingZones.value);
        }

        // Initialize zone completion tracking
        _initializeZoneCompletion(zones.length);

        // Check if zones are available
        if (zones.isEmpty) {
          return _buildEmptyStateWidget(noZones: true);
        }

        // Get the current zone
        final currentZone = zones[controller.currentKnittingZoneIndex.value];

        // Get the zone's instructions
        final zoneInstructions = currentZone.instructions;

        if (zoneInstructions.isEmpty) {
          return _buildEmptyStateWidget();
        }

        // Get knitting row information for the current zone
        final rowInfo =
            _getZoneRowInformation(zoneInstructions, currentZone, context);

        // Initialize row completion tracking
        _initializeCompletedRows(rowInfo.rowsToKnit);

        return SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Determine if we're on a small device (phone), medium (tablet), or large (desktop)
              final isSmallDevice = constraints.maxWidth < 600;
              final isLargeDevice = constraints.maxWidth >= 1024;

              // For larger devices, use a different layout with side-by-side elements
              if (isLargeDevice) {
                return _buildDesktopLayout(
                    context, rowInfo, constraints, zones, currentZone);
              } else {
                // For phone and tablet, use a scrollable column layout with adjusted sizing
                return _buildMobileOrTabletLayout(context, rowInfo, constraints,
                    isSmallDevice, zones, currentZone);
              }
            },
          ),
        );
      }),
    );
  }

  /// Ensures the current zone index is valid
  void _ensureValidZoneIndex() {
    // Get all zones
    final List<KnittingZone> zones = [];
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeController = Get.find<ShapeEditorController>();
      zones.addAll(
          shapeController.knittingInstructionsManager.knittingZones.value);
    }

    // If no zones, nothing to do
    if (zones.isEmpty) return;

    // If current zone index is out of bounds, reset it
    if (controller.currentKnittingZoneIndex.value >= zones.length) {
      controller.currentKnittingZoneIndex.value = 0;

      // Reset current row to uninitialized state - let _getZoneRowInformation handle proper initialization
      controller.currentKnittingRow.value = -1;

      controller.saveWizardProgress();
      debugPrint(
          "[InteractiveKnittingView] Reset zone index to 0 and row to uninitialized state");
    }

    // Initialize zone completion tracking
    _initializeZoneCompletion(zones.length);
  }

  /// Build layout for desktop-sized screens
  Widget _buildDesktopLayout(
      BuildContext context,
      KnittingRowInfo rowInfo,
      BoxConstraints constraints,
      List<KnittingZone> zones,
      KnittingZone currentZone) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left panel: Progress tracker with shape visualization
        Expanded(
          flex: 3,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Progress tracker
                _buildProgressTracker(
                  context,
                  rowInfo.currentRowIndex,
                  rowInfo.totalRows,
                  rowInfo.progress,
                  isLargeDisplay: true,
                  currentZone: currentZone,
                ),
              ],
            ),
          ),
        ),

        // Right panel: Instructions and navigation
        Expanded(
          flex: 4,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Obx(
                  () => controller.isConfigInfoOpen.value
                      ? _buildZoneConfigInfo(currentZone)
                      : const SizedBox.shrink(),
                ),

                const SizedBox(height: 16),

                // Previous button (top)
                _buildPreviousButton(rowInfo.prevAction, rowInfo.rowsToKnit,
                    rowInfo.currentRowIndex),

                const SizedBox(height: 16),

                // Knitting instructions
                _buildKnittingInstructions(
                  rowInfo,
                  controller.newItem.value.knittingMachine?.needlesCount ?? 100,
                  currentZone,
                ),

                const SizedBox(height: 16),

                // Next button (bottom)
                _buildNextButton(rowInfo.nextAction, rowInfo.rowsToKnit,
                    rowInfo.currentRowIndex, currentZone),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build layout for mobile and tablet-sized screens
  Widget _buildMobileOrTabletLayout(
    BuildContext context,
    KnittingRowInfo rowInfo,
    BoxConstraints constraints,
    bool isSmallDevice,
    List<KnittingZone> zones,
    KnittingZone currentZone,
  ) {
    // Adjust padding based on device size
    final horizontalPadding = isSmallDevice ? 12.0 : 16.0;

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: 12.0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),

            Obx(() {
              return controller.isConfigInfoOpen.value
                  ? _buildZoneConfigInfo(currentZone)
                  : const SizedBox.shrink();
            }),

            const SizedBox(height: 12),

            // Progress tracker - smaller height on small devices
            _buildProgressTracker(
              context,
              rowInfo.currentRowIndex,
              rowInfo.totalRows,
              rowInfo.progress,
              height: isSmallDevice ? 220.0 : 300.0,
              currentZone: currentZone,
            ),

            const SizedBox(height: 16),

            // Previous button
            _buildPreviousButton(rowInfo.prevAction, rowInfo.rowsToKnit,
                rowInfo.currentRowIndex),

            const SizedBox(height: 12),

            // Knitting instructions
            _buildKnittingInstructions(
              rowInfo,
              controller.newItem.value.knittingMachine?.needlesCount ?? 100,
              currentZone,
            ),

            const SizedBox(height: 12),

            // Next button
            _buildNextButton(rowInfo.nextAction, rowInfo.rowsToKnit,
                rowInfo.currentRowIndex, currentZone),
          ],
        ),
      ),
    );
  }

  /// Build zone configuration info card
  Widget _buildZoneConfigInfo(KnittingZone zone) {
    // Get config values
    final autoCarriagePosition =
        zone.config.value.autoControlCarriagePosition.value;
    final carriagePosition = zone.config.value.carriagePosition.value;
    final autoAdjustAsymmetrical =
        zone.config.value.autoAdjustAsymmetricalRows.value;
    final asymmetricalDirection = zone.config.value.asymmetricalDirection.value;
    final autoControlIncreasesDecreases =
        zone.config.value.autoControlIncreaseDecrease.value;
    final isEmpty = zone.config.value.isEmpty.value;
    final finishingMethod = zone.config.value.finishingMethod.value;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Get.theme.colorScheme.primary.withOpacity(0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'knittingZone_configuration'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.primary,
                    ),
                  ),
                ),

                //close button
                InkWell(
                    onTap: controller.closeConfigInfo,
                    child: const Icon(Icons.close, size: 18)),
              ],
            ),
            const Divider(),
            if (isEmpty)
              // Empty zone config
              Row(
                children: [
                  const Icon(Icons.info_outline, size: 16, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(
                    '${'knittingZone_finishingMethod'.tr}: ${finishingMethod == "bind off" ? 'knittingZone_bindOffMethod'.tr : 'knittingZone_useScrapMethod'.tr}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              )
            else
              // Regular zone configs
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.swap_horiz,
                          size: 16, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        autoCarriagePosition
                            ? 'knittingZone_autoCarriagePositionControl'.tr
                            : 'knittingZone_carriageStartsOn'.trParams({
                                'side': carriagePosition == "left"
                                    ? 'knittingZone_left'.tr.toUpperCase()
                                    : 'knittingZone_right'.tr.toUpperCase()
                              }),
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.balance, size: 16, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        autoAdjustAsymmetrical
                            ? 'knittingZone_autoAdjustAsymmetricalRowsText'.tr
                            : 'knittingZone_adjustBy'.trParams({
                                'direction': asymmetricalDirection == "inc"
                                    ? 'knittingZone_increasing'.tr
                                    : 'knittingZone_decreasing'.tr
                              }),
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.trending_up,
                          size: 16, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        autoControlIncreasesDecreases
                            ? 'knittingZone_autoControlIncreasesDecreasesText'
                                .tr
                            : 'knittingZone_manualIncreasesDecreases'.tr,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// Get row information and navigation functions based on current zone
  KnittingRowInfo _getZoneRowInformation(List<List<bool>> zoneInstructions,
      KnittingZone zone, BuildContext context) {
    final currentRowIndex = controller.currentKnittingRow.value;
    final totalRows = zoneInstructions.length;

    // Handle uninitialized state (-1) or invalid row index
    int validRowIndex;
    if (currentRowIndex == -1 || currentRowIndex >= totalRows) {
      // First time entering or invalid index - start at bottom row (last in array for bottom-up knitting)
      validRowIndex = totalRows - 1;
      // Update the controller to reflect the initialized state
      controller.currentKnittingRow.value = validRowIndex;
      controller.saveWizardProgress();
      debugPrint(
          "[InteractiveKnittingView] Initialized knitting row to bottom position: $validRowIndex");
    } else {
      validRowIndex = currentRowIndex;
    }

    // Ensure validRowIndex is within bounds
    validRowIndex = validRowIndex.clamp(0, totalRows - 1);

    // Calculate the user-facing row number (1-based, from bottom to top)
    // If array index is N-1, user sees row 1; if array index is 0, user sees row N
    final userFacingRowNum = totalRows - validRowIndex;

    // Apply zone configurations before processing instructions
    final processedZoneInstructions =
        ZoneConfigurationProcessor.applyZoneConfiguration(zone);

    // Process instructions for the current zone with the processed instructions
    final processedInstructions =
        _processZoneInstructions(processedZoneInstructions, zone);

    // Find which instruction we're currently on
    int currentInstructionIndex = -1;
    KnittingInstruction? currentInstruction;

    // Progress should be (totalRows - currentRowIndex - 1) / totalRows
    // This gives 0% at the start (currentRowIndex = totalRows-1) and 100% at the end (currentRowIndex = 0)
    double progress =
        totalRows > 0 ? (totalRows - validRowIndex - 1) / totalRows : 0.0;

    // Find the current instruction based on the row index
    for (int i = 0; i < processedInstructions.length; i++) {
      final instruction = processedInstructions[i];
      final instructionEndRow =
          instruction.rowIndex + instruction.repeatCount - 1;

      // Check if current row is within this instruction's range
      if (validRowIndex >= instruction.rowIndex &&
          validRowIndex <= instructionEndRow) {
        currentInstructionIndex = i;
        currentInstruction = instruction;
        break;
      }
    }

    // If we still haven't found a matching instruction, find the one that's closest
    if (currentInstructionIndex == -1 && processedInstructions.isNotEmpty) {
      // Find closest instruction
      for (int i = 0; i < processedInstructions.length; i++) {
        final instruction = processedInstructions[i];
        final instructionEndRow =
            instruction.rowIndex + instruction.repeatCount - 1;

        if (validRowIndex < instruction.rowIndex &&
            (i == 0 || validRowIndex > processedInstructions[i - 1].rowIndex)) {
          // Current row is before this instruction but after the previous one
          currentInstructionIndex = i;
          currentInstruction = instruction;
          break;
        } else if (validRowIndex > instructionEndRow &&
            (i == processedInstructions.length - 1 ||
                validRowIndex < processedInstructions[i + 1].rowIndex)) {
          // Current row is after this instruction but before the next one
          currentInstructionIndex = i;
          currentInstruction = instruction;
          break;
        }
      }

      // If still not found, just use the first instruction
      if (currentInstructionIndex == -1) {
        currentInstructionIndex = 0;
        currentInstruction = processedInstructions.first;
      }
    }

    int rowsToKnit = currentInstruction?.repeatCount ?? 1;
    int startRow = currentInstruction?.rowIndex ?? validRowIndex;
    int endRow = startRow + rowsToKnit - 1;

    // Calculate user-facing row numbers for the current instruction range
    final userStartRow = totalRows - endRow;
    final userEndRow = totalRows - startRow;

    // Get navigation actions with zone transitions
    final navigationActions = _getNavigationActionsForZone(
        processedInstructions, currentInstructionIndex, context, zone);

    return KnittingRowInfo(
      currentRowIndex: validRowIndex,
      displayRowNum: userFacingRowNum,
      totalRows: totalRows,
      rowsToKnit: rowsToKnit,
      startRow: startRow,
      endRow: endRow,
      userStartRow: userStartRow,
      userEndRow: userEndRow,
      progress: progress,
      prevAction: navigationActions.prevAction,
      nextAction: navigationActions.nextAction,
      isBottomUp: true,
    );
  }

  /// Process instructions for a specific zone
  List<KnittingInstruction> _processZoneInstructions(
      List<List<bool>> zoneInstructions, KnittingZone zone) {
    final processed = <KnittingInstruction>[];

    if (zoneInstructions.isEmpty) return processed;

    int currentRow = 0;
    final needleCount =
        controller.newItem.value.knittingMachine?.needlesCount ?? 100;

    while (currentRow < zoneInstructions.length) {
      // Check for repeating rows
      final repeatCount = _findRepeatingRows(currentRow, zoneInstructions);

      if (repeatCount > 1) {
        // Found repeating rows
        final row = zoneInstructions[currentRow];
        final ranges = utils.KnittingUtils.findStitchRanges(row);

        // Adjust ranges to absolute needle positions
        final adjustedRanges = _adjustStitchRangesToAbsolutePositions(
            ranges, zone, zoneInstructions, row);

        processed.add(KnittingInstruction(
          type: InstructionType.repeat,
          rowIndex: currentRow,
          repeatCount: repeatCount,
          stitchRanges: adjustedRanges,
          displayText: _generateInstructionText(adjustedRanges, repeatCount,
              needleCount: needleCount),
        ));

        currentRow += repeatCount;
      } else {
        // Process a single row
        final row = zoneInstructions[currentRow];
        final ranges = utils.KnittingUtils.findStitchRanges(row);
        final hasDiscontinuous = ranges.length > 1;

        // Adjust ranges to absolute needle positions
        final adjustedRanges = _adjustStitchRangesToAbsolutePositions(
            ranges, zone, zoneInstructions, row);

        processed.add(KnittingInstruction(
          type: hasDiscontinuous
              ? InstructionType.discontinuous
              : InstructionType.single,
          rowIndex: currentRow,
          repeatCount: 1,
          stitchRanges: adjustedRanges,
          displayText: _generateInstructionText(adjustedRanges, 1,
              needleCount: needleCount),
          warnings: hasDiscontinuous ? ['Discontinuous pattern'] : [],
        ));

        currentRow++;
      }
    }

    return processed;
  }

  /// Adjust stitch ranges to absolute needle positions based on zone configuration
  List<StitchRange> _adjustStitchRangesToAbsolutePositions(
      List<StitchRange> ranges,
      KnittingZone zone,
      List<List<bool>> processedInstructions,
      List<bool> currentRow) {
    if (ranges.isEmpty) return ranges;

    // Calculate zone shift due to increases/decreases
    int zoneShift = 0;
    if (processedInstructions.isNotEmpty &&
        zone.instructions.isNotEmpty &&
        processedInstructions[0].length != zone.instructions[0].length) {
      zoneShift =
          (processedInstructions[0].length - zone.instructions[0].length) ~/ 2;
    }

    // Adjust each range to absolute positions
    return ranges.map((range) {
      final adjustedStart = zone.startNeedle + range.startNeedle - zoneShift;
      final adjustedEnd = zone.startNeedle + range.endNeedle - zoneShift;

      return StitchRange(adjustedStart, adjustedEnd);
    }).toList();
  }

  /// Find how many consecutive repeating rows there are starting from startRow
  int _findRepeatingRows(int startRow, List<List<bool>> patternInstructions) {
    if (startRow >= patternInstructions.length) return 0;

    int count = 1;
    final baseRow = patternInstructions[startRow];

    for (int i = startRow + 1; i < patternInstructions.length; i++) {
      if (utils.KnittingUtils.rowsAreSimilar(baseRow, patternInstructions[i])) {
        count++;
      } else {
        break;
      }
    }

    return count;
  }

  /// Generate instruction text for stitch ranges
  String _generateInstructionText(List<StitchRange> ranges, int repeatCount,
      {required int needleCount}) {
    final rangesText = utils.KnittingUtils.formatRangesText(
      ranges,
      needleCount,
      useLRNotation: true,
    );

    if (repeatCount > 1) {
      // For repeating rows, add repeat information
      return 'Repeat for $repeatCount rows: $rangesText';
    }

    return rangesText;
  }

  /// Get navigation actions for instruction-based navigation within a zone
  /// and handling transition between zones
  _NavigationActions _getNavigationActionsForZone(
    List<KnittingInstruction> instructions,
    int currentInstructionIndex,
    BuildContext context,
    KnittingZone currentZone,
  ) {
    VoidCallback? prevAction;
    VoidCallback? nextAction;

    // Get all zones
    final List<KnittingZone> zones = [];
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeController = Get.find<ShapeEditorController>();
      zones.addAll(
          shapeController.knittingInstructionsManager.knittingZones.value);
    }

    if (currentInstructionIndex > 0) {
      // Previous instruction is the one with higher row indices (moving down in the pattern)
      // This is going "backward" in the instructions list but "forward" in the array indices
      nextAction = () {
        // Jump to the previous instruction's starting row
        final prevInstruction = instructions[currentInstructionIndex - 1];
        controller.currentKnittingRow.value = prevInstruction.rowIndex;
        controller.saveWizardProgress(); // Save progress after navigation
      };
    } else {
      // At the first instruction (bottom of the pattern)

      if (controller.currentKnittingZoneIndex < zones.length - 1) {
        // More zones to go
        nextAction = () {
          // Mark current zone as completed
          _markCurrentZoneCompleted();

          // Show confirmation dialog for zone completion
          _showZoneCompletionDialog(context, currentZone,
              zones[controller.currentKnittingZoneIndex.value + 1]);
        };
      } else {
        // This is the last zone - show completion action
        nextAction = () {
          // Mark current zone as completed
          _markCurrentZoneCompleted();

          // Mark all zones as completed for good measure - both local and controller state
          final allCompleted = List.generate(zones.length, (_) => true);
          completedZones.value = allCompleted;
          controller.completedKnittingZones.value = List.from(allCompleted);

          // Show final completion dialog
          _showCompletionDialog(context);
        };
      }
    }

    if (currentInstructionIndex < instructions.length - 1) {
      // Next instruction is the one with lower row indices (moving up in the pattern)
      // This is going "forward" in the instructions list but "backward" in the array indices
      prevAction = () {
        // Jump to the next instruction's starting row
        final nextInstruction = instructions[currentInstructionIndex + 1];
        controller.currentKnittingRow.value = nextInstruction.rowIndex;
        controller.saveWizardProgress(); // Save progress after navigation

        // Show a quick toast to confirm the action
        Fluttertoast.showToast(
          msg: 'knittingInstructions_interactive_movingToPrevious'.tr,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      };
    }

    return _NavigationActions(
      prevAction: prevAction,
      nextAction: nextAction,
    );
  }

  /// Show dialog when a zone is completed
  void _showZoneCompletionDialog(
      BuildContext context, KnittingZone completedZone, KnittingZone nextZone) {
    StandardDialog.show<void>(
      context: context,
      title: 'knittingInstructions_interactive_zoneCompleted'
          .trParams({'zoneName': completedZone.name}),
      content: 'knittingInstructions_interactive_zoneCompletedMessage'.trParams(
          {'completedZone': completedZone.name, 'nextZone': nextZone.name}),
      actions: [
        DialogAction.text(
          text: 'knittingInstructions_interactive_restartZone'.tr,
          onPressed: () {
            Navigator.of(context).pop();

            // Restart the zone
            // Reset row to uninitialized state so it gets properly initialized for the new zone
            controller.currentKnittingRow.value = -1;
          },
        ),
        DialogAction.elevated(
          text: 'knittingInstructions_interactive_startNextZone'.tr,
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          onPressed: () {
            // First close the dialog
            Navigator.of(context).pop();

            // Move to next zone with a slight delay to ensure UI update
            Future.microtask(() {
              // Update the current zone index
              controller.currentKnittingZoneIndex.value++;

              // Reset row to uninitialized state so it gets properly initialized for the new zone
              controller.currentKnittingRow.value = -1;

              // Save progress
              controller.saveWizardProgress();

              // Show toast to confirm zone transition
              Fluttertoast.showToast(
                msg: 'knittingInstructions_interactive_nowKnitting'
                    .trParams({'zoneName': nextZone.name}),
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.CENTER,
                backgroundColor: Get.theme.colorScheme.primary,
                textColor: Get.theme.colorScheme.onPrimary,
              );
            });
          },
        ),
      ],
    );
  }

  /// Show completion dialog when knitting is finished
  void _showCompletionDialog(BuildContext context) {
    StandardDialog.show<void>(
      context: context,
      title: 'knittingInstructions_interactive_patternCompleted'.tr,
      titleIcon: Icon(Icons.check_circle, color: Colors.green, size: 24),
      customContent: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'knittingInstructions_interactive_patternCompletedMessage'.tr,
            style: TextStyle(
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'knittingInstructions_interactive_whatNext'.tr,
            style: TextStyle(
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
      actions: [
        DialogAction.outlined(
          text: 'knittingInstructions_interactive_startOver'.tr,
          onPressed: () {
            Navigator.of(context).pop();
            // Restart from first zone
            controller.currentKnittingZoneIndex.value = 0;

            // Reset row to uninitialized state
            controller.currentKnittingRow.value = -1;

            // Reset zone completion tracking - both local and controller state
            if (completedZones.isNotEmpty) {
              final newList =
                  List.generate(completedZones.length, (_) => false);
              completedZones.value = newList;
              // Also reset controller's state
              controller.completedKnittingZones.value = List.from(newList);
            }
            controller.saveWizardProgress();

            Fluttertoast.showToast(
              msg: 'knittingInstructions_interactive_patternResetMessage'.tr,
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
            );
          },
        ),
        DialogAction.elevated(
          text: 'knittingInstructions_interactive_myItems'.tr,
          backgroundColor: Colors.purple,
          foregroundColor: Colors.white,
          onPressed: () {
            Navigator.of(context).pop();
            controller.completeWizard().then((_) {
              Get.rootDelegate.popRoute();
            });
          },
        ),
      ],
    );
  }

  /// Build empty state widget when no instructions are available
  Widget _buildEmptyStateWidget({bool noZones = false}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pattern,
            size: 80,
            color: Get.theme.colorScheme.primary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            noZones
                ? 'knittingInstructions_interactive_noZonesAvailable'.tr
                : 'knittingInstructions_interactive_noInstructionsAvailable'.tr,
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            noZones
                ? 'knittingInstructions_interactive_configureZonesFirst'.tr
                : 'knittingInstructions_interactive_shapeDataMissing'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Get.theme.colorScheme.onBackground.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              controller.isLoading.value = true;
              controller.generateKnittingInstructions().then((_) {
                controller.isLoading.value = false;
              });
            },
            icon: const Icon(Icons.refresh),
            label: Text(
                'knittingInstructions_interactive_regenerateInstructions'.tr),
            style: ElevatedButton.styleFrom(
              backgroundColor: Get.theme.colorScheme.primary,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(
          Icons.cancel,
          size: 30,
        ),
        tooltip: 'common_close'.tr,
        onPressed: () async {
          final bool? res = await StandardDialog.show<bool>(
            context: context,
            title: 'common_stopKnitting'.tr,
            content: 'knittingInstructions_interactive_confirmClose'.tr,
            actions: [
              DialogAction.outlined(
                text: 'knittingInstructions_interactive_startOver'.tr,
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
              ),
              DialogAction.elevated(
                text: 'knittingInstructions_interactive_stayHere'.tr,
                onPressed: () => Navigator.of(context).pop(false),
              ),
            ],
          );
          if (res == true) {
            // Reset to uninitialized state
            controller.currentKnittingRow.value = -1;
            // Navigate back to the shape editor step
            controller.goToStep(2);
          }
        },
      ),
      title: Obx(() {
        return Text(controller.newItem.value.name ?? 'common_untitled'.tr,
            overflow: TextOverflow.ellipsis);
      }),
      actions: [
        IconButton(
          icon: const Icon(Icons.print),
          tooltip: 'knittingInstructions_print_printPattern'.tr,
          onPressed: () {
            final instructions = controller.knittingInstructions.value;
            if (instructions.isEmpty) {
              Get.snackbar(
                'knittingInstructions_print_printError'.tr,
                'knittingInstructions_print_noPatternAvailable'.tr,
                snackPosition: SnackPosition.BOTTOM,
              );
              return;
            }

            _printPattern(context);
          },
        ),
      ],
    );
  }

  void _printPattern(BuildContext context) {
    final instructions = controller.knittingInstructions.value;
    final itemData = controller.newItem.value;

    if (instructions.isEmpty) {
      Get.snackbar(
        'knittingInstructions_print_printError'.tr,
        'knittingInstructions_print_noPatternAvailable'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // Get shapes from controller
    List<ShapeData> shapes = [];
    try {
      if (Get.isRegistered<ShapeEditorController>()) {
        final shapeEditorController = Get.find<ShapeEditorController>();
        for (final shape in shapeEditorController.shapes) {
          if (shape.key != null) {
            final shapeData = shapeEditorController.getShapeState(shape.key);
            if (shapeData != null) {
              shapes.add(shapeData);
            }
          }
        }
      }
    } catch (e) {
      // Handle error silently
    }

    // Show loading indicator
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    // Use our pattern printer service
    PatternPrinterService()
        .printPattern(
      itemData: itemData,
      instructions: instructions,
      shapes: shapes,
      patternStatistics: controller.patternStatistics.value,
      context: context,
    )
        .then((_) {
      // Close loading indicator
      Get.back();
      Get.snackbar(
        'knittingInstructions_print_printSent'.tr,
        'knittingInstructions_print_patternSentToPrinter'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }).catchError((error) {
      // Close loading indicator
      Get.back();
      Get.snackbar(
        'knittingInstructions_print_printError'.tr,
        'knittingInstructions_print_failedToPrint'
            .trParams({'error': error.toString()}),
        snackPosition: SnackPosition.BOTTOM,
      );
    });
  }

  /// Build the previous button
  Widget _buildPreviousButton(
      VoidCallback? prevAction, int rowsToKnit, int currentRowIndex) {
    // Calculate row ranges for navigation buttons
    final totalRows = controller.knittingInstructions.value.length;

    // Get the zones
    final List<KnittingZone> zones = [];
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeController = Get.find<ShapeEditorController>();
      zones.addAll(
          shapeController.knittingInstructionsManager.knittingZones.value);
    }

    // Get current zone
    final currentZone = controller.currentKnittingZoneIndex.value < zones.length
        ? zones[controller.currentKnittingZoneIndex.value]
        : null;

    if (currentZone == null) return Container(); // Safety check

    // Process all instructions to find the actual next set of rows
    final processedInstructions =
        _processZoneInstructions(currentZone.instructions, currentZone);

    // Find current instruction index
    int currentInstructionIndex = -1;
    for (int i = 0; i < processedInstructions.length; i++) {
      final instruction = processedInstructions[i];
      final instructionEndRow =
          instruction.rowIndex + instruction.repeatCount - 1;

      if (currentRowIndex >= instruction.rowIndex &&
          currentRowIndex <= instructionEndRow) {
        currentInstructionIndex = i;
        break;
      }
    }

    // Check if there's a next instruction (going upward in the pattern)
    String previousRowsText = '';
    if (currentInstructionIndex < processedInstructions.length - 1 &&
        currentInstructionIndex >= 0) {
      final nextInstruction =
          processedInstructions[currentInstructionIndex + 1];
      final instructionEndRow =
          nextInstruction.rowIndex + nextInstruction.repeatCount - 1;

      // Calculate user-facing row numbers (1-based from bottom to top)
      final zoneRows = currentZone.instructions.length;
      final userStartRow = zoneRows - instructionEndRow;
      final userEndRow = zoneRows - nextInstruction.rowIndex;

      previousRowsText = '$userStartRow-$userEndRow';
    }

    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: prevAction,
        icon: const Icon(Icons.arrow_upward, size: 14),
        label: Text(
          previousRowsText.isNotEmpty
              ? 'knittingInstructions_interactive_previousRows'
                  .trParams({'rows': previousRowsText})
              : 'common_previous'.tr,
          style: const TextStyle(fontSize: 12),
        ),
        style: OutlinedButton.styleFrom(
          foregroundColor: Get.theme.colorScheme.primary,
          side: BorderSide(color: Get.theme.colorScheme.primary),
          padding: const EdgeInsets.symmetric(vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      ),
    );
  }

  /// Build the next button
  Widget _buildNextButton(VoidCallback? nextAction, int rowsToKnit,
      int currentRowIndex, KnittingZone currentZone) {
    // Calculate row ranges for navigation buttons
    final zoneRows = currentZone.instructions.length;

    // Process all instructions to find the actual next set of rows
    final processedInstructions =
        _processZoneInstructions(currentZone.instructions, currentZone);

    // Find current instruction index
    int currentInstructionIndex = -1;
    for (int i = 0; i < processedInstructions.length; i++) {
      final instruction = processedInstructions[i];
      final instructionEndRow =
          instruction.rowIndex + instruction.repeatCount - 1;

      if (currentRowIndex >= instruction.rowIndex &&
          currentRowIndex <= instructionEndRow) {
        currentInstructionIndex = i;
        break;
      }
    }

    // Check if there's a previous instruction (going downward in the pattern)
    String nextRowsText = '';
    if (currentInstructionIndex > 0) {
      final prevInstruction =
          processedInstructions[currentInstructionIndex - 1];
      final instructionEndRow =
          prevInstruction.rowIndex + prevInstruction.repeatCount - 1;

      // Calculate user-facing row numbers (1-based from bottom to top)
      final userStartRow = zoneRows - instructionEndRow;
      final userEndRow = zoneRows - prevInstruction.rowIndex;

      nextRowsText = '$userStartRow-$userEndRow';
    }

    // Get all zones to determine if this is the last zone
    final List<KnittingZone> zones = [];
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeController = Get.find<ShapeEditorController>();
      zones.addAll(
          shapeController.knittingInstructionsManager.knittingZones.value);
    }

    final isLastZone =
        controller.currentKnittingZoneIndex.value >= zones.length - 1;
    final isFirstInstruction = currentInstructionIndex <= 0;

    // Button text depends on whether this is the end of the zone
    String buttonText;
    if (isFirstInstruction) {
      // At the end of the current zone
      buttonText = isLastZone
          ? 'knittingInstructions_interactive_completeSequence'.tr
          : 'knittingInstructions_interactive_completeZoneAndContinue'.tr;
    } else {
      // Within the zone
      buttonText = nextRowsText.isNotEmpty
          ? 'knittingInstructions_interactive_completeAndContinue'
              .trParams({'rows': nextRowsText})
          : 'knittingInstructions_interactive_completeSequence'.tr;
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: nextAction,
        icon: const Icon(Icons.arrow_downward, size: 14),
        label: Text(
          buttonText,
          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Get.theme.colorScheme.primary,
          foregroundColor: Get.theme.colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      ),
    );
  }

  /// Build the progress tracker widget with shape visualization
  Widget _buildProgressTracker(
      BuildContext context, int currentRow, int totalRows, double progress,
      {double height = 300.0,
      bool isLargeDisplay = false,
      required KnittingZone currentZone}) {
    // Get the shape data from the controller
    List<ShapeData> shapes = [];
    try {
      if (Get.isRegistered<ShapeEditorController>()) {
        final shapeEditorController = Get.find<ShapeEditorController>();
        for (final shape in shapeEditorController.shapes) {
          if (shape.key != null) {
            final shapeData = shapeEditorController.getShapeState(shape.key);
            if (shapeData != null) {
              shapes.add(shapeData);
            }
          }
        }
      }
    } catch (e) {
      // Handle errors silently
    }

    // Increase preview size significantly for better visibility
    // Use larger dimensions for both mobile and desktop
    final Size previewSize =
        Size(isLargeDisplay ? 600 : 540, isLargeDisplay ? 600 : 540);

    // Get full pattern info and all zones
    final fullPatternInstructions = controller.knittingInstructions.value;
    final List<KnittingZone> zones = [];
    if (Get.isRegistered<ShapeEditorController>()) {
      final shapeController = Get.find<ShapeEditorController>();
      zones.addAll(
          shapeController.knittingInstructionsManager.knittingZones.value);
    }

    // Get the configuration info for display
    final hasCustomConfig =
        !currentZone.config.value.autoAdjustAsymmetricalRows.value ||
            !currentZone.config.value.autoControlIncreaseDecrease.value;

    final configInfo = hasCustomConfig
        ? [
            if (!currentZone.config.value.autoAdjustAsymmetricalRows.value)
              ZoneConfigurationProcessor.getAsymmetricalAdjustmentText(
                  currentZone.config.value),
            if (!currentZone.config.value.autoControlIncreaseDecrease.value)
              ZoneConfigurationProcessor.getIncreaseDecreaseText(
                  currentZone.config.value),
          ].join(' • ')
        : null;

    // Get needle ranges for the current zone
    final needleRanges = _getCurrentZoneNeedleRanges(currentZone);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Get.theme.colorScheme.primary, width: 1),
      ),
      // Reduce padding to maximize space for the pattern
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  '${currentZone.name} - ${'knittingInstructions_interactive_progressTracker'.tr}',
                  style: TextStyle(
                    color: Get.theme.colorScheme.primary,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // Add carriage position info
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.swap_horiz, size: 14),
                    const SizedBox(width: 4),
                    Text(
                      ZoneConfigurationProcessor.getCarriagePositionText(
                          currentZone.config.value),
                      style: TextStyle(
                        fontSize: 12,
                        color: Get.theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Add configuration info if custom settings are applied
          if (hasCustomConfig)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.primary.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: Get.theme.colorScheme.primary.withOpacity(0.2),
                  ),
                ),
                child: Text(
                  'knittingInstructions_interactive_customSettings'
                      .trParams({'settings': configInfo ?? ''}),
                  style: TextStyle(
                    fontSize: 12,
                    color: Get.theme.colorScheme.primary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ),

          // Increase height for better visibility
          SizedBox(
            width: double.infinity,
            // Increase height based on display size
            height: isLargeDisplay ? height * 1.4 : height * 1.3,
            child: Center(
              child: Obx(() {
                return CustomPaint(
                  size: previewSize,
                  painter: ZoneAwareProgressPainter(
                    shapes: shapes,
                    fullPatternInstructions: fullPatternInstructions,
                    zones: zones,
                    currentZoneIndex: controller.currentKnittingZoneIndex.value,
                    currentRow: currentRow,
                    totalRows:
                        totalRows, // Pass totalRows as a parameter but it's not used internally
                    stitchesPerCm:
                        controller.newItem.value.stitchesPerCm ?? 2.0,
                    rowsPerCm: controller.newItem.value.rowsPerCm ?? 2.5,
                    progressPercentage: progress,
                    context: context,
                    topStitches: _calculateZoneTopStitches(currentZone),
                    bottomStitches: _calculateZoneBottomStitches(currentZone),
                    topWidth:
                        "${DimensionUtils.formatCmForDisplay(_calculateZoneTopWidth(currentZone))} ${'knittingInstructions_summary_cm'.tr}",
                    bottomWidth:
                        "${DimensionUtils.formatCmForDisplay(_calculateZoneBottomWidth(currentZone))} ${'knittingInstructions_summary_cm'.tr}",
                    height:
                        "${DimensionUtils.formatCmForDisplay(_calculateZoneHeight(currentZone))} ${'knittingInstructions_summary_cm'.tr}",
                    rowsCount: totalRows,
                    leftNeedles: needleRanges['left'] ?? 0,
                    rightNeedles: needleRanges['right'] ?? 0,
                    completedZones:
                        completedZones.toList(), // Pass completion data
                    isPatternCompleted: _isPatternCompleted(
                        zones), // Check if entire pattern is completed
                    zonePositionInfo: {
                      'startNeedle': currentZone.startNeedle,
                      'endNeedle': currentZone.endNeedle,
                      'startRow': currentZone.startRow,
                      'endRow': currentZone.endRow,
                      'fullPatternWidth': fullPatternInstructions.isNotEmpty
                          ? fullPatternInstructions.first.length
                          : 100,
                      'fullPatternHeight': fullPatternInstructions.length,
                      // Add needle positioning information for the visual context
                      'absoluteLeftNeedle':
                          needleRanges['absoluteLeftmostNeedle'],
                      'absoluteRightNeedle':
                          needleRanges['absoluteRightmostNeedle'],
                      'startNeedleLabel': needleRanges['startNeedle'],
                      'endNeedleLabel': needleRanges['endNeedle'],
                      'needleCount': controller
                              .newItem.value.knittingMachine?.needlesCount ??
                          100,
                      'useAbsolutePositions':
                          true, // Flag to tell the painter to use absolute positions
                    },
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// Helper methods to calculate dimensions for a specific zone
  int _calculateZoneTopStitches(KnittingZone zone) {
    return zone.instructions.isNotEmpty
        ? zone.instructions.first.where((stitch) => stitch).length
        : 0;
  }

  int _calculateZoneBottomStitches(KnittingZone zone) {
    return zone.instructions.isNotEmpty
        ? zone.instructions.last.where((stitch) => stitch).length
        : 0;
  }

  double _calculateZoneTopWidth(KnittingZone zone) {
    final stitchesPerCm =
        controller.newItem.value.stitchesPerCm ?? 1.0; // Use 1.0 as fallback
    final topStitches = _calculateZoneTopStitches(zone);
    return DimensionUtils.stitchesToCm(topStitches, stitchesPerCm);
  }

  double _calculateZoneBottomWidth(KnittingZone zone) {
    final stitchesPerCm =
        controller.newItem.value.stitchesPerCm ?? 1.0; // Use 1.0 as fallback
    final bottomStitches = _calculateZoneBottomStitches(zone);
    return DimensionUtils.stitchesToCm(bottomStitches, stitchesPerCm);
  }

  double _calculateZoneHeight(KnittingZone zone) {
    final rowsPerCm =
        controller.newItem.value.rowsPerCm ?? 1.0; // Use 1.0 as fallback
    final totalRows = zone.instructions.length;
    return DimensionUtils.rowsToCm(totalRows, rowsPerCm);
  }

  /// Get the needle ranges for the current row in the current zone
  Map<String, dynamic> _getCurrentZoneNeedleRanges(KnittingZone zone) {
    final needleCount =
        controller.newItem.value.knittingMachine?.needlesCount ?? 100;

    // Apply zone configurations to get processed instructions
    final processedInstructions =
        ZoneConfigurationProcessor.applyZoneConfiguration(zone);

    if (processedInstructions.isNotEmpty) {
      final currentRowIndex = controller.currentKnittingRow.value;
      final validRowIndex =
          math.min(currentRowIndex, zone.instructions.length - 1);

      if (validRowIndex >= 0) {
        // Use the new method that properly handles absolute positioning
        return ZoneConfigurationProcessor.getAdjustedNeedleRanges(
          zone,
          processedInstructions,
          validRowIndex,
          needleCount,
        );
      }
    }

    // Fallback - use zone boundaries directly when no specific row information is available
    final centerNeedle = needleCount ~/ 2;

    // Calculate left and right positions based on zone boundaries
    final startNeedleLabel = utils.KnittingUtils.formatNeedleNumber(
        zone.startNeedle, needleCount,
        useLRNotation: true);

    final endNeedleLabel = utils.KnittingUtils.formatNeedleNumber(
        zone.endNeedle, needleCount,
        useLRNotation: true);

    int leftNeedles = 0;
    int rightNeedles = 0;

    // Calculate left needle count
    if (zone.startNeedle < centerNeedle) {
      leftNeedles = centerNeedle - zone.startNeedle;
    }

    // Calculate right needle count
    if (zone.endNeedle >= centerNeedle) {
      rightNeedles = zone.endNeedle - centerNeedle + 1;
    }

    return {
      'left': leftNeedles,
      'right': rightNeedles,
      'startNeedle': startNeedleLabel,
      'endNeedle': endNeedleLabel,
      'zoneStartNeedle': zone.startNeedle,
      'zoneEndNeedle': zone.endNeedle,
      'zoneStartRow': zone.startRow,
      'zoneEndRow': zone.endRow,
      'absoluteLeftmostNeedle': zone.startNeedle,
      'absoluteRightmostNeedle': zone.endNeedle,
    };
  }

  /// Helper method to display zone position info in the UI
  String _formatZonePositionInfo(KnittingZone zone) {
    final needleCount =
        controller.newItem.value.knittingMachine?.needlesCount ?? 100;
    final centerNeedle = needleCount ~/ 2;

    // Format start needle
    String startLabel = zone.startNeedle < centerNeedle
        ? "L${centerNeedle - zone.startNeedle}"
        : "R${zone.startNeedle - centerNeedle + 1}";

    // Format end needle
    String endLabel = zone.endNeedle < centerNeedle
        ? "L${centerNeedle - zone.endNeedle}"
        : "R${zone.endNeedle - centerNeedle + 1}";

    // Calculate rows from top
    final patternRows = controller.knittingInstructions.value.length;
    final rowFromTop = zone.startRow + 1; // 1-based
    final rowToTop = zone.endRow + 1; // 1-based

    return 'knittingZone_positionFormat'.trParams(
        {'needles': '$startLabel-$endLabel', 'rows': '$rowFromTop-$rowToTop'});
  }

  /// Build the knitting instructions card for the current section
  Widget _buildKnittingInstructions(
      KnittingRowInfo rowInfo, int needleCount, KnittingZone currentZone) {
    // For UI display, we use user-facing row numbers (row 1 is the bottom-most/last row)
    final String displayStartingRow = '${rowInfo.userStartRow}';
    final String displayEndingRow = '${rowInfo.userEndRow}';

    // Get the actual needle range using the current row info
    final needleRange = _getCurrentZoneNeedleRanges(currentZone);
    final startNeedle = needleRange['startNeedle'];
    final endNeedle = needleRange['endNeedle'];

    // Get theme colors
    final primaryColor = Get.theme.colorScheme.primary;
    final surfaceColor = Get.theme.colorScheme.surface;
    final onSurfaceColor = Get.theme.colorScheme.onSurface;
    final onPrimaryColor = Get.theme.colorScheme.onPrimary;

    // Get carriage position from zone config - with improved description
    final carriagePositionDisplay =
        ZoneConfigurationProcessor.getCarriagePositionText(
            currentZone.config.value);

    // Get asymmetrical adjustment status
    final asymmetricalAdjustment =
        ZoneConfigurationProcessor.getAsymmetricalAdjustmentText(
            currentZone.config.value);

    // Get increase/decrease status
    final increaseDecreaseStatus =
        ZoneConfigurationProcessor.getIncreaseDecreaseText(
            currentZone.config.value);

    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'knittingInstructions_interactive_knittingInstructions'
                        .trParams({'count': rowInfo.rowsToKnit.toString()}),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                  ),
                ),
                // Add carriage position indicator
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.swap_horiz, size: 14),
                      const SizedBox(width: 4),
                      Text(
                        'interactiveKnitting_carriage'.tr +
                            ": $carriagePositionDisplay",
                        style: TextStyle(
                          fontSize: 12,
                          color: primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Add configuration info section
            if (!currentZone.config.value.autoAdjustAsymmetricalRows.value ||
                !currentZone.config.value.autoControlIncreaseDecrease.value)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: primaryColor.withOpacity(0.2)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (!currentZone
                          .config.value.autoAdjustAsymmetricalRows.value)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            children: [
                              Icon(Icons.balance,
                                  size: 14, color: primaryColor),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  'interactiveKnitting_asymmetrical'.tr +
                                      ": $asymmetricalAdjustment",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: primaryColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      if (!currentZone
                          .config.value.autoControlIncreaseDecrease.value)
                        Row(
                          children: [
                            Icon(Icons.trending_up,
                                size: 14, color: primaryColor),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                'interactiveKnitting_shaping'.tr +
                                    ": $increaseDecreaseStatus",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: primaryColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Create a row with cross layout that adapts to available width
            LayoutBuilder(
              builder: (context, constraints) {
                // Determine if we should use a compact layout
                final useCompactLayout = constraints.maxWidth < 400;

                if (useCompactLayout) {
                  return _buildCompactCrossLayout(
                    displayStartingRow,
                    displayEndingRow,
                    startNeedle,
                    endNeedle,
                    primaryColor,
                    surfaceColor,
                    onSurfaceColor,
                    onPrimaryColor,
                  );
                } else {
                  return _buildRegularCrossLayout(
                    displayStartingRow,
                    displayEndingRow,
                    startNeedle,
                    endNeedle,
                    primaryColor,
                    surfaceColor,
                    onSurfaceColor,
                    onPrimaryColor,
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Build regular cross layout for wider screens
  Widget _buildRegularCrossLayout(
    String displayStartingRow,
    String displayEndingRow,
    String startNeedle,
    String endNeedle,
    Color primaryColor,
    Color surfaceColor,
    Color onSurfaceColor,
    Color onPrimaryColor,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Left column with labels
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Starting row counter label
              Text(
                'knittingInstructions_interactive_startingRowCounter'.tr,
                style: TextStyle(
                  fontSize: 14,
                  color: primaryColor,
                ),
              ),
              const SizedBox(height: 24),

              // Needle settings label
              Text(
                'knittingInstructions_interactive_needleSettings'.tr,
                style: TextStyle(
                  fontSize: 14,
                  color: primaryColor,
                ),
              ),

              const SizedBox(height: 24),

              // Ending row counter label
              Text(
                'knittingInstructions_interactive_endingRowCounter'.tr,
                style: TextStyle(
                  fontSize: 14,
                  color: primaryColor,
                ),
              ),
            ],
          ),
        ),

        // Right cross layout
        Expanded(
          flex: 3,
          child: SizedBox(
            height: 120, // Fixed height for the cross
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Top - Starting row counter
                Positioned(
                  top: 0,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                    decoration: BoxDecoration(
                      color: surfaceColor,
                      border: Border.all(color: primaryColor),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      displayStartingRow,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: onSurfaceColor,
                      ),
                    ),
                  ),
                ),

                // Center - Needle settings
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 6),
                      decoration: BoxDecoration(
                        color: primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '$startNeedle',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: onPrimaryColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 6),
                      decoration: BoxDecoration(
                        color: primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '$endNeedle',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: onPrimaryColor,
                        ),
                      ),
                    ),
                  ],
                ),

                // Bottom - Ending row counter
                Positioned(
                  bottom: 0,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                    decoration: BoxDecoration(
                      color: surfaceColor,
                      border: Border.all(color: primaryColor),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      displayEndingRow,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: onSurfaceColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build compact cross layout for narrower screens
  Widget _buildCompactCrossLayout(
    String displayStartingRow,
    String displayEndingRow,
    String startNeedle,
    String endNeedle,
    Color primaryColor,
    Color surfaceColor,
    Color onSurfaceColor,
    Color onPrimaryColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Top section - Starting row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                'knittingInstructions_interactive_startingRowCounter'.tr,
                style: TextStyle(
                  fontSize: 14,
                  color: primaryColor,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: surfaceColor,
                  border: Border.all(color: primaryColor),
                  borderRadius: BorderRadius.circular(16),
                ),
                alignment: Alignment.center,
                child: Text(
                  displayStartingRow,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: onSurfaceColor,
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Middle section - Needle settings
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                'knittingInstructions_interactive_needleSettings'.tr,
                style: TextStyle(
                  fontSize: 14,
                  color: primaryColor,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 6),
                      decoration: BoxDecoration(
                        color: primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        '$startNeedle',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: onPrimaryColor,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 6),
                      decoration: BoxDecoration(
                        color: primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        '$endNeedle',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: onPrimaryColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Bottom section - Ending row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                'knittingInstructions_interactive_endingRowCounter'.tr,
                style: TextStyle(
                  fontSize: 14,
                  color: primaryColor,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: surfaceColor,
                  border: Border.all(color: primaryColor),
                  borderRadius: BorderRadius.circular(16),
                ),
                alignment: Alignment.center,
                child: Text(
                  displayEndingRow,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: onSurfaceColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Check if the entire pattern is completed
  bool _isPatternCompleted(List<KnittingZone> zones) {
    // If there are no zones, pattern is not completed
    if (zones.isEmpty || completedZones.isEmpty) return false;

    // Check if all zones are completed
    for (int i = 0; i < zones.length && i < completedZones.length; i++) {
      if (!completedZones[i]) {
        return false;
      }
    }

    return true;
  }
}

/// Data class for row information
class KnittingRowInfo {
  final int currentRowIndex; // Array index (0-based)
  final int displayRowNum; // User-facing row number (1-based)
  final int totalRows; // Total number of rows
  final int rowsToKnit; // Number of rows to knit in current instruction
  final int startRow; // Internal array start row index
  final int endRow; // Internal array end row index
  final int userStartRow; // User-facing start row number
  final int userEndRow; // User-facing end row number
  final double progress; // Progress percentage
  final VoidCallback? prevAction;
  final VoidCallback? nextAction;
  final bool isBottomUp;

  KnittingRowInfo({
    required this.currentRowIndex,
    required this.displayRowNum,
    required this.totalRows,
    required this.rowsToKnit,
    required this.startRow,
    required this.endRow,
    required this.userStartRow,
    required this.userEndRow,
    required this.progress,
    required this.prevAction,
    required this.nextAction,
    required this.isBottomUp,
  });
}

/// Helper class for navigation actions
class _NavigationActions {
  final VoidCallback? prevAction;
  final VoidCallback? nextAction;

  _NavigationActions({
    required this.prevAction,
    required this.nextAction,
  });
}

/// Dialog to preview the entire knitting pattern at once
class KnittingPreviewDialog extends StatelessWidget {
  final NewItemWizardController controller;
  final List<List<bool>> instructions;

  const KnittingPreviewDialog({
    super.key,
    required this.controller,
    required this.instructions,
  });

  @override
  Widget build(BuildContext context) {
    final needleCount =
        controller.newItem.value.knittingMachine?.needlesCount ?? 100;
    final maxWidth = instructions.isNotEmpty
        ? instructions.map((row) => row.length).reduce(math.max)
        : 0;
    final totalRows = instructions.length;

    return Dialog(
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and close button
            Row(
              children: [
                const Icon(FontAwesomeIcons.glasses, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'knittingInstructions_interactive_preview_patternPreview'
                        .trParams({
                      'name':
                          controller.newItem.value.name ?? 'common_untitled'.tr
                    }),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),

            // Pattern stats
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatChip(
                    icon: Icons.straighten,
                    label:
                        'knittingInstructions_interactive_preview_stitchesWide'
                            .trParams({'width': maxWidth.toString()}),
                  ),
                  _buildStatChip(
                    icon: Icons.height,
                    label: 'knittingInstructions_interactive_preview_rowsTall'
                        .trParams({'rows': totalRows.toString()}),
                  ),
                  _buildStatChip(
                    icon: Icons.grid_4x4,
                    label: 'knittingInstructions_interactive_preview_needles'
                        .trParams({
                      'left': (needleCount ~/ 2).toString(),
                      'right': (needleCount ~/ 2).toString()
                    }),
                  ),
                ],
              ),
            ),

            // Full pattern visualization
            Expanded(
              child: EnhancedPatternVisualizer(
                pattern: instructions,
                needleCount: needleCount,
                useLRNotation: true,
                onRowSelected: (rowIndex) {
                  // Update current row and close dialog
                  controller.currentKnittingRow.value = rowIndex;
                  Navigator.of(context).pop();
                },
                onStitchTap: null, // Disable stitch tap in preview
              ),
            ),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.print),
                  label: Text('knittingInstructions_print_printPattern'.tr),
                  onPressed: () {
                    Navigator.of(context).pop();
                    Future.delayed(const Duration(milliseconds: 300), () {
                      _printPattern(context);
                    });
                  },
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('common_close'.tr),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper to build stat chips
  Widget _buildStatChip({required IconData icon, required String label}) {
    return Chip(
      backgroundColor: Colors.white,
      avatar: Icon(icon, size: 16, color: Colors.purple[800]),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  // Print method (referenced from main class)
  void _printPattern(BuildContext context) {
    // Get required data
    final instructions = controller.knittingInstructions.value;
    final itemData = controller.newItem.value;

    if (instructions.isEmpty) {
      Get.snackbar(
        'knittingInstructions_print_printError'.tr,
        'knittingInstructions_print_noPatternAvailable'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // Get shapes from controller
    List<ShapeData> shapes = [];
    try {
      if (Get.isRegistered<ShapeEditorController>()) {
        final shapeEditorController = Get.find<ShapeEditorController>();
        for (final shape in shapeEditorController.shapes) {
          if (shape.key != null) {
            final shapeData = shapeEditorController.getShapeState(shape.key);
            if (shapeData != null) {
              shapes.add(shapeData);
            }
          }
        }
      }
    } catch (e) {
      // Handle error silently
    }

    // Show loading indicator
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    // Use our pattern printer service
    PatternPrinterService()
        .printPattern(
      itemData: itemData,
      instructions: instructions,
      shapes: shapes,
      patternStatistics: controller.patternStatistics.value,
      context: context,
    )
        .then((_) {
      // Close loading indicator
      Get.back();
      Get.snackbar(
        'knittingInstructions_print_printSent'.tr,
        'knittingInstructions_print_patternSentToPrinter'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }).catchError((error) {
      // Close loading indicator
      Get.back();
      Get.snackbar(
        'knittingInstructions_print_printError'.tr,
        'knittingInstructions_print_failedToPrint'
            .trParams({'error': error.toString()}),
        snackPosition: SnackPosition.BOTTOM,
      );
    });
  }
}

/// A widget that displays knitting instructions in a text-based format
/// with improved UI based on the EnhancedPatternVisualizer's text view
class TextInstructionsView extends StatelessWidget {
  final List<List<bool>> instructions;
  final int currentRowIndex;
  final int rowsToKnit;
  final int needleCount;
  final Function(int)? onRowSelected;

  const TextInstructionsView({
    super.key,
    required this.instructions,
    required this.currentRowIndex,
    required this.rowsToKnit,
    required this.needleCount,
    this.onRowSelected,
  });

  @override
  Widget build(BuildContext context) {
    if (instructions.isEmpty || currentRowIndex >= instructions.length) {
      return Card(
        margin: const EdgeInsets.symmetric(vertical: 12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
              'knittingInstructions_interactive_noInstructionsAvailable'.tr),
        ),
      );
    }

    // Process the pattern into instructions - similar to EnhancedPatternVisualizer
    final processedInstructions = _processInstructions();

    // Find the instruction relevant to the current row
    KnittingInstruction? currentInstruction;
    for (final instruction in processedInstructions) {
      if (currentRowIndex >= instruction.rowIndex &&
          currentRowIndex < instruction.rowIndex + instruction.repeatCount) {
        currentInstruction = instruction;
        break;
      }
    }

    if (currentInstruction == null) {
      return Card(
        margin: const EdgeInsets.symmetric(vertical: 12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text('errors_noCurrentInstructionFound'.tr),
        ),
      );
    }

    // Now that we've confirmed currentInstruction is not null, we can proceed safely
    return _buildInstructionCard(context, currentInstruction);
  }

  /// Build the main instruction card with non-null instruction
  Widget _buildInstructionCard(
      BuildContext context, KnittingInstruction currentInstruction) {
    // Match the style from _buildTextView() in EnhancedPatternVisualizer
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: ListTile(
        selected: true,
        selectedTileColor: Colors.purple.withOpacity(0.1),
        title: Text(
          _getInstructionTitle(currentInstruction),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            // For repeat instructions, streamline the text to avoid duplication
            if (currentInstruction.type == InstructionType.repeat)
              _buildHighlightedNeedleRanges(currentInstruction)
            else
              _buildHighlightedNeedleRanges(currentInstruction),

            // Show warnings if any
            if (currentInstruction.warnings.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Divider(height: 1),
              for (final warning in currentInstruction.warnings)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      const Icon(Icons.warning_amber,
                          size: 16, color: Colors.orange),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          warning,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.orange,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ],
        ),
        isThreeLine: currentInstruction.warnings.isNotEmpty ||
            currentInstruction.type == InstructionType.repeat,
        leading: CircleAvatar(
          backgroundColor: _getInstructionColor(currentInstruction.type),
          child: _getInstructionIcon(currentInstruction.type),
        ),
        trailing: currentInstruction.type == InstructionType.repeat
            ? Chip(
                label: Text('${currentInstruction.repeatCount}×'),
                backgroundColor: Colors.amber.withOpacity(0.2),
              )
            : null,
      ),
    );
  }

  /// Build highlighted needle ranges for better visibility
  Widget _buildHighlightedNeedleRanges(KnittingInstruction instruction) {
    final rangesText = utils.KnittingUtils.formatRangesText(
      instruction.stitchRanges,
      needleCount,
      useLRNotation: true,
    );

    // Split the text by commas to separately style each range
    final ranges = rangesText.split(', ');

    if (ranges.isEmpty) {
      return const Text('No stitches to knit');
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: ranges.map((range) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
          decoration: BoxDecoration(
            color: Colors.purple.withOpacity(0.1),
            border: Border.all(color: Colors.purple.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            range.trim(),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Get current progress within a repeat instruction
  String _getCurrentRepeatProgress(KnittingInstruction instruction) {
    final repeatOffset = currentRowIndex - instruction.rowIndex;
    final currentRepeatProgress = repeatOffset + 1;
    return 'Row $currentRepeatProgress of ${instruction.repeatCount}';
  }

  /// Get color for instruction type
  Color _getInstructionColor(InstructionType type) {
    switch (type) {
      case InstructionType.single:
        return Colors.purple;
      case InstructionType.repeat:
        return Colors.amber.shade700;
      case InstructionType.discontinuous:
        return Colors.orange;
    }
  }

  /// Process the pattern into a list of instructions
  List<KnittingInstruction> _processInstructions() {
    final processed = <KnittingInstruction>[];

    if (instructions.isEmpty) return processed;

    int currentRow = 0;

    while (currentRow < instructions.length) {
      // Check for repeating rows
      final repeatCount = _findRepeatingRows(currentRow);

      if (repeatCount > 1) {
        // Found repeating rows
        final row = instructions[currentRow];
        final ranges = utils.KnittingUtils.findStitchRanges(row);

        processed.add(KnittingInstruction(
          type: InstructionType.repeat,
          rowIndex: currentRow,
          repeatCount: repeatCount,
          stitchRanges: ranges,
          displayText: _generateInstructionText(ranges, repeatCount),
        ));

        currentRow += repeatCount;
      } else {
        // Process a single row
        final row = instructions[currentRow];
        final ranges = utils.KnittingUtils.findStitchRanges(row);
        final hasDiscontinuous = ranges.length > 1;

        processed.add(KnittingInstruction(
          type: hasDiscontinuous
              ? InstructionType.discontinuous
              : InstructionType.single,
          rowIndex: currentRow,
          repeatCount: 1,
          stitchRanges: ranges,
          displayText: _generateInstructionText(ranges, 1),
          warnings: hasDiscontinuous ? ['Discontinuous pattern'] : [],
        ));

        currentRow++;
      }
    }

    return processed;
  }

  /// Find how many consecutive repeating rows there are starting from startRow
  int _findRepeatingRows(int startRow) {
    if (startRow >= instructions.length) return 0;

    int count = 1;
    final baseRow = instructions[startRow];

    for (int i = startRow + 1; i < instructions.length; i++) {
      if (utils.KnittingUtils.rowsAreSimilar(baseRow, instructions[i])) {
        count++;
      } else {
        break;
      }
    }

    return count;
  }

  /// Generate instruction text for stitch ranges
  String _generateInstructionText(List<StitchRange> ranges, int repeatCount) {
    final rangesText = utils.KnittingUtils.formatRangesText(
      ranges,
      needleCount,
      useLRNotation: true,
    );

    if (repeatCount > 1) {
      // For repeating rows, simplify the text to avoid duplication in the UI
      return rangesText;
    }

    return rangesText;
  }

  /// Get an instruction title based on type and row information
  String _getInstructionTitle(KnittingInstruction instruction) {
    switch (instruction.type) {
      case InstructionType.single:
        return 'Row ${instruction.rowIndex + 1}';
      case InstructionType.repeat:
        final startRow = instruction.rowIndex + 1; // 1-indexed
        final endRow = startRow + instruction.repeatCount - 1;
        return 'Rows $startRow-$endRow';
      case InstructionType.discontinuous:
        return 'Row ${instruction.rowIndex + 1}';
    }
  }

  /// Get an appropriate icon for the instruction type
  Widget _getInstructionIcon(InstructionType type) {
    switch (type) {
      case InstructionType.single:
        return const Icon(Icons.straighten, size: 20, color: Colors.white);
      case InstructionType.repeat:
        return const Icon(Icons.repeat, size: 20, color: Colors.white);
      case InstructionType.discontinuous:
        return const Icon(Icons.warning, size: 20, color: Colors.white);
    }
  }
}

/// Enum representing types of knitting instructions
enum InstructionType {
  single,
  repeat,
  discontinuous,
}

/// Class representing a processed knitting instruction
class KnittingInstruction {
  final InstructionType type;
  final int rowIndex;
  final int repeatCount;
  final List<StitchRange> stitchRanges;
  final String displayText;
  final List<String> warnings;

  KnittingInstruction({
    required this.type,
    required this.rowIndex,
    required this.repeatCount,
    required this.stitchRanges,
    required this.displayText,
    this.warnings = const [],
  });
}
